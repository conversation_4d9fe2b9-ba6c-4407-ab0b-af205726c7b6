import { Arg, Directive, Field, ObjectType, Resolver, Root, Subscription } from 'type-graphql'
import { AppEventType } from '../../../app/types/AppEventType.js'
import { BaseGraphQlResolver } from '../../../app/types/services.js'
import { ServiceName } from '../../../app/types/enums.js'
import { INatsService } from '../../nats/types/INatsService.js'
import { NatsStreamName } from '../../../app/types/NatsStreamName.js'
import { NatsSubject } from '../../../app/types/NatsSubject.js'
import appHelpers from '../../../app/helpers.js'
import logger from '../../logger/index.js'
import serviceHelpers from '../serviceHelpers/index.js'

// Helper function to create NATS subscription and bridge to GraphQL
async function createNatsSubscriptionBridge(
  streamName: string,
  subject?: string,
  consumerName?: string
): Promise<void> {
  try {
    const natsService = appHelpers.getService<INatsService>(ServiceName.nats)

    if (!natsService) {
      logger.error('NATS service not available for subscription bridge')
      return
    }

    // Generate unique consumer name if not provided
    const finalConsumerName = consumerName || `graphql_sub_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`

    logger.info('Creating NATS subscription bridge', {
      streamName,
      subject,
      consumerName: finalConsumerName
    })

    // Create the bridge processor
    const processor = async (message: any) => {
      try {
        const pubSub = serviceHelpers.getPubSub()
        if (!pubSub) {
          logger.error('GraphQL pubSub not available in NATS bridge')
          return
        }

        // Decode the message data
        const data = new TextDecoder().decode(message.data)

        // Create the payload for GraphQL subscription
        const payload = {
          message: data,
          natsMetadata: {
            subject: message.subject,
            sequence: message.seq,
            timestamp: new Date().toISOString(),
            streamName: message.info?.stream,
            consumerName: finalConsumerName
          }
        }

        // Publish to GraphQL subscription
        pubSub.publish(AppEventType.natsStreamMessage, payload)

        logger.debug('NATS message bridged to GraphQL subscription', {
          subject: message.subject,
          sequence: message.seq,
          streamName: message.info?.stream
        })

      } catch (error) {
        logger.error('Error in NATS to GraphQL bridge processor', {
          error,
          subject: message.subject,
          sequence: message.seq
        })
      }
    }

    // Create the NATS subscription
    await natsService.subscribe(
      streamName as NatsStreamName,
      subject as NatsSubject | undefined,
      finalConsumerName as any,
      processor
    )

    logger.info('NATS subscription bridge created successfully', {
      streamName,
      subject,
      consumerName: finalConsumerName
    })

  } catch (error) {
    logger.error('Error creating NATS subscription bridge', {
      error,
      streamName,
      subject,
      consumerName
    })
  }
}

@ObjectType()
export class NatsMetadata {
  @Field()
  subject!: string

  @Field()
  sequence!: number

  @Field()
  timestamp!: string

  @Field({ nullable: true })
  streamName?: string

  @Field({ nullable: true })
  consumerName?: string
}

@ObjectType()
export class NatsMessage {
  @Field()
  message!: string

  @Field()
  subject!: string

  @Field()
  natsMetadata!: NatsMetadata

  @Field({ nullable: true })
  data?: string
}


@Resolver()
export class NatsMessageResolver extends BaseGraphQlResolver {

  @Subscription(returns => NatsMessage, {
    topics: AppEventType.natsStreamMessage,
    filter: ({ payload, args }) => {
      if (!payload || !payload.natsMetadata) {
        return false
      }

      // Filter by subject pattern if provided
      if (args.subjectPattern) {
        const regex = new RegExp(args.subjectPattern)
        return regex.test(payload.natsMetadata.subject)
      }

      return true
    },
  })
  @Directive('@rateLimit(limit: 100, duration: 60)')
  async natsMessages(
    @Arg('streamName') streamName: string,
    @Arg('subject', { nullable: true }) subject?: string,
    @Arg('consumerName', { nullable: true }) consumerName?: string,
    @Arg('subjectPattern', { nullable: true }) subjectPattern?: string,
    @Root() payload?: any
  ): Promise<NatsMessage> {
    // Automatically create NATS subscription when GraphQL subscription is established
    createNatsSubscriptionBridge(streamName, subject, consumerName)
      .catch(error => {
        logger.error('Failed to create NATS subscription bridge', {
          error,
          streamName,
          subject,
          consumerName
        })
      })

    return {
      message: payload.message || JSON.stringify(payload),
      subject: payload.natsMetadata.subject,
      natsMetadata: payload.natsMetadata,
      data: typeof payload === 'object' ? JSON.stringify(payload) : payload
    }
  }
}
