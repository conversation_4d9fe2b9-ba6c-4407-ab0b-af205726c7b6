import { AppEventType } from '../../../app/types/AppEventType.js'
import { BgChannelChangedEvent } from '../../bgChannels/types/classes/BgChannelChangedEvent.js'
import { ObjectChangedEvent } from '../../models/types/classes/ObjectChangedEvent.js'

type PubSubPublishArgsByKey = {
  [key: string]: [] | [any] | [number | string, any];
};

// Type for NATS stream messages
export interface NatsStreamMessagePayload {
  message: string;
  natsMetadata: {
    subject: string;
    sequence: number;
    timestamp: string;
    streamName?: string;
    consumerName?: string;
  };
}

export interface SubscriptionsType extends PubSubPublishArgsByKey {
  [AppEventType.objectChanged]: [ObjectChangedEvent];
  [AppEventType.channelChanged]: [BgChannelChangedEvent];
  [AppEventType.natsStreamMessage]: [NatsStreamMessagePayload];
}
