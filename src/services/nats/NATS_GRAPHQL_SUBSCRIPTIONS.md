# NATS GraphQL Subscriptions

This document describes the GraphQL subscriptions available for receiving NATS messages in real-time.

## 🚀 **NEW: Automatic NATS Subscription Creation**

The GraphQL subscriptions now **automatically create NATS subscriptions** when you subscribe to them! No need to manually create NATS subscriptions first.

## Available Subscriptions

### 1. `natsMessages` - Automatic NATS Message Subscription

Subscribe to NATS messages and automatically create the NATS subscription.

```graphql
subscription NatsMessages(
  $streamName: String!
  $subject: String
  $consumerName: String
  $subjectPattern: String
) {
  natsMessages(
    streamName: $streamName
    subject: $subject
    consumerName: $consumerName
    subjectPattern: $subjectPattern
  ) {
    message
    subject
    natsMetadata {
      subject
      sequence
      timestamp
      streamName
      consumerName
    }
    data
  }
}
```

**Parameters:**
- `streamName` (required): NATS stream name to subscribe to
- `subject` (optional): Specific subject to subscribe to
- `consumerName` (optional): Custom consumer name (auto-generated if not provided)
- `subjectPattern` (optional): Regex pattern to filter messages by subject

**Example Usage:**
```graphql
# Subscribe to messages from a specific stream
subscription {
  natsMessages(streamName: "CHANNEL_MESSAGES") {
    message
    subject
    natsMetadata {
      subject
      sequence
      timestamp
      streamName
      consumerName
    }
  }
}

# Subscribe to specific subject with custom consumer
subscription {
  natsMessages(
    streamName: "CHANNEL_MESSAGES"
    subject: "first.spark.dev.channel.*.messages"
    consumerName: "my-custom-consumer"
  ) {
    message
    subject
    natsMetadata {
      subject
      sequence
      timestamp
      streamName
      consumerName
    }
  }
}

# Subscribe with subject pattern filtering
subscription {
  natsMessages(
    streamName: "CHANNEL_MESSAGES"
    subjectPattern: "first\\.spark\\.dev\\.channel\\..*\\.messages"
  ) {
    message
    subject
    data
  }
}
```

### 2. `natsStreamMessages` - Enhanced NATS Stream Message Subscription

Subscribe to NATS stream messages with advanced filtering options.

```graphql
subscription NatsStreamMessages(
  $streamName: String
  $subjectPattern: String
  $consumerName: String
) {
  natsStreamMessages(
    streamName: $streamName
    subjectPattern: $subjectPattern
    consumerName: $consumerName
  ) {
    id
    subject
    message
    data
    natsMetadata {
      subject
      sequence
      timestamp
      streamName
    }
    headers
    receivedAt
  }
}
```

**Parameters:**
- `streamName` (optional): Filter by specific NATS stream name
- `subjectPattern` (optional): Regex pattern to filter messages by subject
- `consumerName` (optional): Filter by specific consumer name

**Example Usage:**
```graphql
# Subscribe to messages from a specific stream
subscription {
  natsStreamMessages(streamName: "FRONTEND_MESSAGES") {
    id
    subject
    message
    natsMetadata {
      subject
      sequence
      timestamp
      streamName
    }
    receivedAt
  }
}

# Subscribe with multiple filters
subscription {
  natsStreamMessages(
    streamName: "FRONTEND_MESSAGES"
    subjectPattern: "frontend\\.notifications"
  ) {
    id
    subject
    message
    data
    headers
    receivedAt
  }
}
```

## Client Implementation Examples

### JavaScript/TypeScript with Apollo Client

```typescript
import { useSubscription, gql } from '@apollo/client'

const NATS_STREAM_MESSAGES_SUBSCRIPTION = gql`
  subscription NatsStreamMessages($streamName: String, $subjectPattern: String) {
    natsStreamMessages(streamName: $streamName, subjectPattern: $subjectPattern) {
      id
      subject
      message
      data
      natsMetadata {
        subject
        sequence
        timestamp
        streamName
      }
      receivedAt
    }
  }
`

function useNatsMessages(streamName?: string, subjectPattern?: string) {
  const { data, loading, error } = useSubscription(NATS_STREAM_MESSAGES_SUBSCRIPTION, {
    variables: { streamName, subjectPattern }
  })

  return {
    message: data?.natsStreamMessages,
    loading,
    error
  }
}

// Usage in React component
function MyComponent() {
  const { message, loading, error } = useNatsMessages(
    'FRONTEND_MESSAGES',
    'frontend\\.notifications'
  )

  useEffect(() => {
    if (message) {
      console.log('New NATS message:', message)
      // Handle the message
    }
  }, [message])

  if (loading) return <div>Connecting...</div>
  if (error) return <div>Error: {error.message}</div>

  return <div>Listening for messages...</div>
}
```

### Vanilla JavaScript

```javascript
// WebSocket connection to GraphQL subscriptions
const ws = new WebSocket('ws://localhost:8092/fsdata/api/graphql', 'graphql-ws')

const subscription = {
  id: '1',
  type: 'start',
  payload: {
    query: `
      subscription {
        natsStreamMessages(streamName: "FRONTEND_MESSAGES") {
          id
          subject
          message
          data
          receivedAt
        }
      }
    `
  }
}

ws.onopen = () => {
  ws.send(JSON.stringify({ type: 'connection_init' }))
  ws.send(JSON.stringify(subscription))
}

ws.onmessage = (event) => {
  const message = JSON.parse(event.data)
  if (message.type === 'data') {
    console.log('Received NATS message:', message.payload.data.natsStreamMessages)
  }
}
```

## 🎯 **Your Specific Use Case**

For your channel messages with subject `"first.spark.dev.channel.*.messages"`:

```graphql
subscription {
  natsMessages(
    streamName: "CHANNEL_MESSAGES"  # Replace with your actual stream name
    subject: "first.spark.dev.channel.*.messages"
    consumerName: "FIRST_SPARK_DEV_CHANNEL_NOTIFICATIONS"
  ) {
    message
    subject
    natsMetadata {
      subject
      sequence
      timestamp
      streamName
      consumerName
    }
    data
  }
}
```

**Variables:**
```json
{
  "streamName": "CHANNEL_MESSAGES",
  "subject": "first.spark.dev.channel.*.messages",
  "consumerName": "FIRST_SPARK_DEV_CHANNEL_NOTIFICATIONS"
}
```

## Complete Workflow (New Simplified Approach)

1. **Just subscribe to GraphQL subscription** - NATS subscription is created automatically:
   ```graphql
   subscription {
     natsMessages(
       streamName: "CHANNEL_MESSAGES"
       subject: "first.spark.dev.channel.*.messages"
       consumerName: "FIRST_SPARK_DEV_CHANNEL_NOTIFICATIONS"
     ) {
       message
       subject
       data
     }
   }
   ```

2. **That's it!** The NATS subscription is automatically created and messages will flow through.

## Message Flow

```
NATS Publisher → NATS Stream → NATS Consumer → GraphQL Bridge → GraphQL Subscription → Client
```

The bridge function automatically:
- Decodes NATS message data
- Creates GraphQL-compatible payload
- Publishes to GraphQL subscription topic
- Acknowledges NATS message
